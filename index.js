//合同金额大小写不一致时属于合同约定不明，在合同履行中容易产生争议。合同经办人起草合同时填写金额要认真，合同审核人员审核合同时对金额大小写要认真核对。
function check(){
    var ctx = context;
    var log = logger;
    var values = ctx.getValues("caseError");
    if(values && values.length > 0){
        logger.info("There are  " + values.length + " values for code:::caseError");
        for(var i=0;i<values.length;i++){
            var txt = values[i].value;
            txt = txt.replace(/\s/g,"");
            txt = txt.replace(/，/g,"");
            txt = txt.replace(/\u002c/g,"");
            txt = txt.replace(/\u002e00([^\d]|$)/g,"");
            txt = txt.replace(/\u002e0([^\d]|$)/g,"");
            var reg = RegExp(/[壹贰叁貳肆伍零陆柒捌玖拾万百千亿佰仟]{2,20}[元圆]([壹贰叁貳肆伍陆零柒捌玖拾万百千亿佰仟]角){0,1}([壹贰叁貳肆伍陆柒捌玖拾万零百千亿佰仟零]分){0,1}/g);
            if(txt.match(reg)){
                var capvalue =txt.match(reg)[0]
                logger.info("capvalue result is  " + capvalue)
                var capitalization = ctx.convertUpCaseMoney(capvalue);
                var reg1 = '[^\\d]'+ capitalization +'([^\\d\\u002e]|$)';
                var reg2 = RegExp(/\u002e\d/g);
                var reg4 = RegExp(/万元/g);
                var result = txt.match(reg1);
                logger.info('result1 is ' + result);
                if(!result && txt.match(reg2)){
                    var reg3 = '[^\\d]'+ capitalization + '0' +'[^\\d]';
                    result = txt.match(reg3);
                    if(!result && txt.match(reg4)){
                        var cap = capitalization/10000;

                        var reg5 = '[^\\d]'+ cap +'[^\\d]';
                        logger.info("capitalization5 cap is " + cap)
                        result = txt.match(reg5);
                    }
                }else if(!result && txt.match(reg4)){
                    var cap = capitalization/10000;
                    var reg6 = '[^\\d]'+ cap +'([^\\d\\u002e]|$)';
                    logger.info("capitalization cap6 is " + cap)
                    result = txt.match(reg6);
                }
                logger.info("capitalization result is " + capitalization + "；  value is " + values[i].value + ";   txt is " + txt + ";  result is " + result);
                if(!result){
                    ctx.getRisk().setKeywords(values[i].originalText);
                    return true;
                }
            }
        }
    }

    return false;
}