/**
 * 合同金额大小写一致性检查函数
 *
 * 功能说明：
 * 合同金额大小写不一致时属于合同约定不明，在合同履行中容易产生争议。
 * 合同经办人起草合同时填写金额要认真，合同审核人员审核合同时对金额大小写要认真核对。
 *
 * caseError 正则表达式解析：
 * 这是一个用于识别合同中可能存在金额大小写不一致问题的复杂正则表达式，主要包含以下几个匹配模式：
 *
 * 模式1: (价(?!.{0:2}\u3011)|合计|总计|人民币|费|实收|预计|暂估|租金|款|共|优惠|资金|付款|支付|酬金|公益金|额|投资|暂定|金额|估算|保证金|\d{1:2}%.{0:6}[计即]|部分：)...
 *   - 匹配金额关键词开头的文本，包括：价格、合计、总计、人民币、各种费用等
 *   - 后跟数字金额和中文大写金额的组合
 *   - 支持百分比表示和各种分隔符
 *
 * 模式2: (价|款|额|费|人民币[：\u003a])[^，。；、]{0:10}[\d￥\u002e\u002c]{4:20}[^。；、\r\n\d大]{0:10}大写...
 *   - 专门匹配明确标注"大写"的金额对比场景
 *   - 用于识别"小写：123.45元，大写：壹佰贰拾叁元肆角伍分"这类格式
 *
 * 模式3: (价|款|额|费)[^，。；、\r\n]{0:10}(大写|人民币)[^\r\n，\。、]\x20{0:4}[壹贰叁貳肆伍陆柒零捌玖拾万百千亿佰仟每元圆角分整]{3:20}...
 *   - 匹配大写在前、小写在后的金额表述
 *   - 处理"大写：壹佰元整，小写：100.00元"这类格式
 *
 * 模式4: 人民币[^\r\n，\。、]{0:2}[壹贰叁貳肆伍陆柒零捌玖拾万百千亿佰仟每元圆角分整\u0009\x20\u0020\u00a0\u3000]{3:20}...
 *   - 匹配以"人民币"开头的金额表述
 *   - 包含中文大写金额和对应的数字金额
 *
 * 模式5: (存款金额|监管额度)[\s\S]{0:10}[壹贰叁貳肆伍陆柒零捌玖拾万百千亿佰仟每元圆角分整\u0009\x20\u0020\u00a0\u3000]{3:20}...
 *   - 专门处理存款金额、监管额度等特殊业务场景
 *   - 匹配这些特定场景下的金额大小写组合
 *
 * 正则表达式中的特殊字符说明：
 * - \u002c: 英文逗号 ","
 * - \u002e: 英文句号 "."
 * - \u003a: 英文冒号 ":"
 * - \u3011: 中文右方括号 "】"
 * - \uff0c: 全角逗号 "，"
 * - \u0009: 制表符
 * - \x20, \u0020, \u00a0, \u3000: 各种空格字符
 * - [﹪%％]: 各种百分号的Unicode变体
 *
 * @returns {boolean} 如果发现金额大小写不一致返回true，否则返回false
 */
function check(){
    // 获取上下文对象和日志对象
    var ctx = context;
    var log = logger;

    // 从上下文中获取可能存在大小写错误的值
    // "caseError" 是一个复杂的正则表达式，用于匹配合同中可能存在金额大小写不一致的文本段落
    // 该正则表达式主要匹配以下几种模式：
    // 1. 包含金额关键词（价、合计、总计、人民币、费用等）+ 数字金额 + 中文大写金额的文本
    // 2. 包含百分比的金额表述
    // 3. 带有"大写"、"小写"标识的金额对比
    // 4. 存款金额、监管额度等特殊场景的金额表述
    var values = ctx.getValues("caseError");

    // 如果存在需要检查的值
    if(values && values.length > 0){
        logger.info("There are  " + values.length + " values for code:::caseError");

        // 遍历每个需要检查的值
        for(var i=0;i<values.length;i++){
            var txt = values[i].value;

            // 文本预处理：清理空格和标点符号
            txt = txt.replace(/\s/g,"");           // 移除所有空白字符
            txt = txt.replace(/，/g,"");           // 移除中文逗号
            txt = txt.replace(/\u002c/g,"");       // 移除英文逗号 (Unicode)
            txt = txt.replace(/\u002e00([^\d]|$)/g,"");  // 移除末尾的.00
            txt = txt.replace(/\u002e0([^\d]|$)/g,"");   // 移除末尾的.0

            // 定义中文大写金额的正则表达式
            // 这个正则表达式用于从 caseError 匹配到的文本中提取中文大写金额部分
            // 匹配规则：
            // - [壹贰叁貳肆伍零陆柒捌玖拾万百千亿佰仟]{2,20}: 2-20个中文大写数字字符
            // - [元圆]: 金额单位（元或圆）
            // - ([壹贰叁貳肆伍陆零柒捌玖拾万百千亿佰仟]角){0,1}: 可选的角位（0或1次）
            // - ([壹贰叁貳肆伍陆柒捌玖拾万零百千亿佰仟零]分){0,1}: 可选的分位（0或1次）
            // 例如：壹万贰千叁佰肆拾伍元陆角柒分、叁万元整 等
            var reg = RegExp(/[壹贰叁貳肆伍零陆柒捌玖拾万百千亿佰仟]{2,20}[元圆]([壹贰叁貳肆伍陆零柒捌玖拾万百千亿佰仟]角){0,1}([壹贰叁貳肆伍陆柒捌玖拾万零百千亿佰仟零]分){0,1}/g);

            // 如果文本中包含中文大写金额
            if(txt.match(reg)){
                // 提取匹配到的大写金额
                var capvalue =txt.match(reg)[0]
                logger.info("capvalue result is  " + capvalue)

                // 将大写金额转换为数字
                var capitalization = ctx.convertUpCaseMoney(capvalue);

                // 构建用于匹配对应数字金额的正则表达式
                var reg1 = '[^\\d]'+ capitalization +'([^\\d\\u002e]|$)';
                var reg2 = RegExp(/\u002e\d/g);    // 匹配小数点后有数字的情况
                var reg4 = RegExp(/万元/g);        // 匹配"万元"单位

                // 在文本中查找对应的数字金额
                var result = txt.match(reg1);
                logger.info('result1 is ' + result);

                // 如果没有找到匹配的数字金额，且文本中有小数
                if(!result && txt.match(reg2)){
                    // 尝试匹配带小数的情况（在数字后加0）
                    var reg3 = '[^\\d]'+ capitalization + '0' +'[^\\d]';
                    result = txt.match(reg3);

                    // 如果仍未找到，且文本中有"万元"
                    if(!result && txt.match(reg4)){
                        // 将金额除以10000转换为万元单位
                        var cap = capitalization/10000;

                        var reg5 = '[^\\d]'+ cap +'[^\\d]';
                        logger.info("capitalization5 cap is " + cap)
                        result = txt.match(reg5);
                    }
                }
                // 如果没有找到匹配的数字金额，但文本中有"万元"
                else if(!result && txt.match(reg4)){
                    // 将金额除以10000转换为万元单位
                    var cap = capitalization/10000;
                    var reg6 = '[^\\d]'+ cap +'([^\\d\\u002e]|$)';
                    logger.info("capitalization cap6 is " + cap)
                    result = txt.match(reg6);
                }

                // 记录检查结果的详细信息
                logger.info("capitalization result is " + capitalization + "；  value is " + values[i].value + ";   txt is " + txt + ";  result is " + result);

                // 如果没有找到对应的数字金额，说明大小写不一致
                if(!result){
                    // 设置风险关键词并返回true表示发现问题
                    ctx.getRisk().setKeywords(values[i].originalText);
                    return true;
                }
            }
        }
    }

    // 如果没有发现大小写不一致的问题，返回false
    return false;
}

/**
 * 整体检查流程总结：
 *
 * 1. 通过 caseError 正则表达式识别合同中可能包含金额大小写对比的文本段落
 * 2. 对每个匹配的文本进行预处理（去除空格、标点等）
 * 3. 使用专门的正则表达式提取其中的中文大写金额
 * 4. 调用 ctx.convertUpCaseMoney() 将中文大写金额转换为对应的数字
 * 5. 在同一文本中查找是否存在对应的数字金额，支持以下几种匹配策略：
 *    - 直接匹配：查找完全相同的数字
 *    - 小数匹配：处理带小数点的情况（如123.00）
 *    - 万元单位匹配：处理以万为单位的金额表示
 * 6. 如果找不到对应的数字金额，则认为存在大小写不一致的问题
 * 7. 将有问题的文本标记为风险关键词，并返回 true
 *
 * 这种多层次的匹配策略能够处理合同中各种复杂的金额表示方式，
 * 确保准确识别出金额大小写不一致的风险点。
 */