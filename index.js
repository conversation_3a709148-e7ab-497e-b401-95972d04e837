/**
 * 合同金额大小写一致性检查函数
 *
 * 功能说明：
 * 合同金额大小写不一致时属于合同约定不明，在合同履行中容易产生争议。
 * 合同经办人起草合同时填写金额要认真，合同审核人员审核合同时对金额大小写要认真核对。
 *
 * @returns {boolean} 如果发现金额大小写不一致返回true，否则返回false
 */
function check(){
    // 获取上下文对象和日志对象
    var ctx = context;
    var log = logger;

    // 从上下文中获取可能存在大小写错误的值
    var values = ctx.getValues("caseError");

    // 如果存在需要检查的值
    if(values && values.length > 0){
        logger.info("There are  " + values.length + " values for code:::caseError");

        // 遍历每个需要检查的值
        for(var i=0;i<values.length;i++){
            var txt = values[i].value;

            // 文本预处理：清理空格和标点符号
            txt = txt.replace(/\s/g,"");           // 移除所有空白字符
            txt = txt.replace(/，/g,"");           // 移除中文逗号
            txt = txt.replace(/\u002c/g,"");       // 移除英文逗号 (Unicode)
            txt = txt.replace(/\u002e00([^\d]|$)/g,"");  // 移除末尾的.00
            txt = txt.replace(/\u002e0([^\d]|$)/g,"");   // 移除末尾的.0

            // 定义中文大写金额的正则表达式
            // 匹配：壹贰叁等大写数字 + 元/圆 + 可选的角分
            var reg = RegExp(/[壹贰叁貳肆伍零陆柒捌玖拾万百千亿佰仟]{2,20}[元圆]([壹贰叁貳肆伍陆零柒捌玖拾万百千亿佰仟]角){0,1}([壹贰叁貳肆伍陆柒捌玖拾万零百千亿佰仟零]分){0,1}/g);

            // 如果文本中包含中文大写金额
            if(txt.match(reg)){
                // 提取匹配到的大写金额
                var capvalue =txt.match(reg)[0]
                logger.info("capvalue result is  " + capvalue)

                // 将大写金额转换为数字
                var capitalization = ctx.convertUpCaseMoney(capvalue);

                // 构建用于匹配对应数字金额的正则表达式
                var reg1 = '[^\\d]'+ capitalization +'([^\\d\\u002e]|$)';
                var reg2 = RegExp(/\u002e\d/g);    // 匹配小数点后有数字的情况
                var reg4 = RegExp(/万元/g);        // 匹配"万元"单位

                // 在文本中查找对应的数字金额
                var result = txt.match(reg1);
                logger.info('result1 is ' + result);

                // 如果没有找到匹配的数字金额，且文本中有小数
                if(!result && txt.match(reg2)){
                    // 尝试匹配带小数的情况（在数字后加0）
                    var reg3 = '[^\\d]'+ capitalization + '0' +'[^\\d]';
                    result = txt.match(reg3);

                    // 如果仍未找到，且文本中有"万元"
                    if(!result && txt.match(reg4)){
                        // 将金额除以10000转换为万元单位
                        var cap = capitalization/10000;

                        var reg5 = '[^\\d]'+ cap +'[^\\d]';
                        logger.info("capitalization5 cap is " + cap)
                        result = txt.match(reg5);
                    }
                }
                // 如果没有找到匹配的数字金额，但文本中有"万元"
                else if(!result && txt.match(reg4)){
                    // 将金额除以10000转换为万元单位
                    var cap = capitalization/10000;
                    var reg6 = '[^\\d]'+ cap +'([^\\d\\u002e]|$)';
                    logger.info("capitalization cap6 is " + cap)
                    result = txt.match(reg6);
                }

                // 记录检查结果的详细信息
                logger.info("capitalization result is " + capitalization + "；  value is " + values[i].value + ";   txt is " + txt + ";  result is " + result);

                // 如果没有找到对应的数字金额，说明大小写不一致
                if(!result){
                    // 设置风险关键词并返回true表示发现问题
                    ctx.getRisk().setKeywords(values[i].originalText);
                    return true;
                }
            }
        }
    }

    // 如果没有发现大小写不一致的问题，返回false
    return false;
}