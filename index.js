/**
 * 合同金额大小写一致性检查函数
 *
 * 功能说明：
 * 合同金额大小写不一致时属于合同约定不明，在合同履行中容易产生争议。
 * 合同经办人起草合同时填写金额要认真，合同审核人员审核合同时对金额大小写要认真核对。
 *
 * 检查逻辑：
 * 1. 从文本中提取中文大写金额（如：叁佰壹拾贰万陆仟伍佰壹拾柒元柒角整）
 * 2. 从文本中提取小写数字金额（如：￥3,126,517.70元）
 * 3. 将两者都转换为以"分"为单位的整数进行精确比较
 * 4. 如果金额不一致，则标记为风险并返回true
 *
 * caseError 正则表达式解析：
 * 这是一个用于识别合同中可能存在金额大小写不一致问题的复杂正则表达式，主要包含以下几个匹配模式：
 *
 * 模式1: (价(?!.{0:2}\u3011)|合计|总计|人民币|费|实收|预计|暂估|租金|款|共|优惠|资金|付款|支付|酬金|公益金|额|投资|暂定|金额|估算|保证金|\d{1:2}%.{0:6}[计即]|部分：)...
 *   - 匹配金额关键词开头的文本，包括：价格、合计、总计、人民币、各种费用等
 *   - 后跟数字金额和中文大写金额的组合
 *   - 支持百分比表示和各种分隔符
 *
 * 模式2: (价|款|额|费|人民币[：\u003a])[^，。；、]{0:10}[\d￥\u002e\u002c]{4:20}[^。；、\r\n\d大]{0:10}大写...
 *   - 专门匹配明确标注"大写"的金额对比场景
 *   - 用于识别"小写：123.45元，大写：壹佰贰拾叁元肆角伍分"这类格式
 *
 * 模式3: (价|款|额|费)[^，。；、\r\n]{0:10}(大写|人民币)[^\r\n，\。、]\x20{0:4}[壹贰叁貳肆伍陆柒零捌玖拾万百千亿佰仟每元圆角分整]{3:20}...
 *   - 匹配大写在前、小写在后的金额表述
 *   - 处理"大写：壹佰元整，小写：100.00元"这类格式
 *
 * 模式4: 人民币[^\r\n，\。、]{0:2}[壹贰叁貳肆伍陆柒零捌玖拾万百千亿佰仟每元圆角分整\u0009\x20\u0020\u00a0\u3000]{3:20}...
 *   - 匹配以"人民币"开头的金额表述
 *   - 包含中文大写金额和对应的数字金额
 *
 * 模式5: (存款金额|监管额度)[\s\S]{0:10}[壹贰叁貳肆伍陆柒零捌玖拾万百千亿佰仟每元圆角分整\u0009\x20\u0020\u00a0\u3000]{3:20}...
 *   - 专门处理存款金额、监管额度等特殊业务场景
 *   - 匹配这些特定场景下的金额大小写组合
 *
 * 正则表达式中的特殊字符说明：
 * - \u002c: 英文逗号 ","
 * - \u002e: 英文句号 "."
 * - \u003a: 英文冒号 ":"
 * - \u3011: 中文右方括号 "】"
 * - \uff0c: 全角逗号 "，"
 * - \u0009: 制表符
 * - \x20, \u0020, \u00a0, \u3000: 各种空格字符
 * - [﹪%％]: 各种百分号的Unicode变体
 *
 * @returns {boolean} 如果发现金额大小写不一致返回true，否则返回false
 */
function check(){
    // 获取上下文对象和日志对象
    var ctx = context;
    var log = logger;

    // 从上下文中获取可能存在大小写错误的值
    // "caseError" 是一个复杂的正则表达式，用于匹配合同中可能存在金额大小写不一致的文本段落
    // 该正则表达式主要匹配以下几种模式：
    // 1. 包含金额关键词（价、合计、总计、人民币、费用等）+ 数字金额 + 中文大写金额的文本
    // 2. 包含百分比的金额表述
    // 3. 带有"大写"、"小写"标识的金额对比
    // 4. 存款金额、监管额度等特殊场景的金额表述
    var values = ctx.getValues("caseError");

    // 如果存在需要检查的值
    if(values && values.length > 0){
        logger.info("There are  " + values.length + " values for code:::caseError");

        // 遍历每个需要检查的值
        for(var i=0;i<values.length;i++){
            var txt = values[i].value;

            // 文本预处理：清理空格和标点符号
            txt = txt.replace(/\s/g,"");           // 移除所有空白字符
            txt = txt.replace(/，/g,"");           // 移除中文逗号
            txt = txt.replace(/\u002c/g,"");       // 移除英文逗号 (Unicode)
            txt = txt.replace(/\u002e00([^\d]|$)/g,"");  // 移除末尾的.00
            txt = txt.replace(/\u002e0([^\d]|$)/g,"");   // 移除末尾的.0

            // 定义中文大写金额的正则表达式
            // 这个正则表达式用于从 caseError 匹配到的文本中提取中文大写金额部分
            // 匹配规则：
            // - [壹贰叁貳肆伍零陆柒捌玖拾万百千亿佰仟]{2,20}: 2-20个中文大写数字字符
            // - [元圆]: 金额单位（元或圆）
            // - ([壹贰叁貳肆伍陆零柒捌玖拾万百千亿佰仟]角){0,1}: 可选的角位（0或1次）
            // - ([壹贰叁貳肆伍陆柒捌玖拾万零百千亿佰仟零]分){0,1}: 可选的分位（0或1次）
            // 例如：壹万贰千叁佰肆拾伍元陆角柒分、叁万元整 等
            var reg = RegExp(/[壹贰叁貳肆伍零陆柒捌玖拾万百千亿佰仟]{2,20}[元圆]([壹贰叁貳肆伍陆零柒捌玖拾万百千亿佰仟]角){0,1}([壹贰叁貳肆伍陆柒捌玖拾万零百千亿佰仟零]分){0,1}/g);

            // 如果文本中包含中文大写金额
            if(txt.match(reg)){
                // 提取匹配到的大写金额
                var capvalue = txt.match(reg)[0];
                logger.info("提取到的中文大写金额: " + capvalue);

                // 将大写金额转换为数字 3126517.7
                var capitalizationInCents = ctx.convertUpCaseMoney(capvalue);
                logger.info("中文大写金额转换为数字（分）: " + capitalizationInCents);

                // 提取文本中的小写数字金额
                // 匹配 ￥ 符号后的数字金额，支持逗号分隔和小数点
                var digitAmountReg = /￥\s*([0-9,]+\.?[0-9]*)/g;
                var digitMatch = txt.match(digitAmountReg);

                if(digitMatch && digitMatch.length > 0){
                    // 提取数字部分，去除￥符号和空格
                    var digitAmountStr = digitMatch[0].replace(/￥\s*/g, '');
                    // 去除逗号分隔符
                    digitAmountStr = digitAmountStr.replace(/,/g, '');
                    // 转换为浮点数
                    var digitAmount = parseFloat(digitAmountStr);

                    logger.info("提取到的小写数字金额: " + digitAmountStr + " 元");
                    logger.info("小写金额转换为数字: " + digitAmount + " 元");

                    // 将小写金额转换为分（避免浮点数精度问题）
                    var digitAmountInCents = Math.round(digitAmount * 100);
                    logger.info("小写金额转换为分: " + digitAmountInCents + " 分");

                    // 比较大写和小写金额是否一致（都以分为单位）
                    if(capitalizationInCents !== digitAmountInCents){
                        logger.info("发现金额不一致！");
                        logger.info("中文大写: " + capvalue + " = " + capitalizationInCents + " 分");
                        logger.info("数字小写: ￥" + digitAmountStr + " = " + digitAmountInCents + " 分");
                        logger.info("差额: " + Math.abs(capitalizationInCents - digitAmountInCents) + " 分");

                        // 设置风险关键词并返回true表示发现问题
                        ctx.getRisk().setKeywords(values[i].originalText);
                        return true;
                    } else {
                        logger.info("金额一致性检查通过");
                        logger.info("中文大写: " + capvalue + " = " + capitalizationInCents + " 分");
                        logger.info("数字小写: ￥" + digitAmountStr + " = " + digitAmountInCents + " 分");
                    }
                } else {
                    logger.info("未能提取到小写数字金额，可能存在格式问题");
                    // 如果无法提取小写金额，也认为是有问题的
                    ctx.getRisk().setKeywords(values[i].originalText);
                    return true;
                }
            }
        }
    }

    // 如果没有发现大小写不一致的问题，返回false
    return false;
}

/**
 * 整体检查流程总结：
 *
 * 1. 通过 caseError 正则表达式识别合同中可能包含金额大小写对比的文本段落
 * 2. 对每个匹配的文本进行预处理（去除空格、标点等）
 * 3. 使用专门的正则表达式提取其中的中文大写金额
 * 4. 调用 ctx.convertUpCaseMoney() 将中文大写金额转换为数字（单位：分）
 * 5. 使用正则表达式 /￥\s*([0-9,]+\.?[0-9]*)/g 提取小写数字金额
 * 6. 将小写金额转换为分（乘以100并四舍五入，避免浮点数精度问题）
 * 7. 比较两个金额的分值是否完全相等
 * 8. 如果不相等，记录详细的差异信息并标记为风险
 * 9. 将有问题的文本标记为风险关键词，并返回 true
 *
 * 示例处理：
 * 输入文本: "费用人民币:叁佰壹拾贰万陆仟伍佰壹拾柒元柒角整(小写￥3,126,517.70元"
 * - 提取中文大写: "叁佰壹拾贰万陆仟伍佰壹拾柒元柒角整"
 * - 转换为分: 312651770 分
 * - 提取小写金额: "3,126,517.70"
 * - 转换为分: 312651770 分
 * - 比较结果: 相等，检查通过
 *
 * 这种精确的数值比较方式能够准确识别出金额大小写不一致的问题，
 * 避免了字符串匹配可能带来的误判。
 */